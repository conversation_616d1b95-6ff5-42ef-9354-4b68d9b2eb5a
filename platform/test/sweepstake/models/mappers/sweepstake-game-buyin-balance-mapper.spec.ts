import { describe, it } from '@tcom/test';
import { anything, instance, mock, reset, verify, when } from '@tcom/test/mock';
import { expect } from '@tcom/test/assert';
import { BigNumber, Config } from '../../../../src/core';
import { Sweepstake, SweepstakeEntrySet, SweepstakeEntrySetManager, SweepstakeGameBuyInBalance, SweepstakeManager, SweepstakeMetadata } from '../../../../src/sweepstake';
import { SweepstakeGameBuyInBalanceCache } from '../../../../src/sweepstake/cache';
import { SweepstakeGameBuyInBalanceModelMapper } from '../../../../src/sweepstake/models/mappers';
import { SweepstakeGameBuyInBalanceModel } from '../../../../src/sweepstake/models/sweepstake-game-buyin-balance.model';
import { SweepstakeEntrySetCodeGenerator } from '../../../../src/sweepstake/utilities/sweepstake-entryset-code.generator';

describe('SweepstakeGameBuyInBalanceModelMapper', () => {
    const mockSweepstakeManager = mock(SweepstakeManager);
    const mockSweepstakeEntrySetManager = mock(SweepstakeEntrySetManager);
    const mockSweepstakeEntrySetCodeGenerator = mock(SweepstakeEntrySetCodeGenerator);
    const mockSweepstakeGameBuyInBalanceCache = mock(SweepstakeGameBuyInBalanceCache);

    function getMapper(): SweepstakeGameBuyInBalanceModelMapper {
        return new SweepstakeGameBuyInBalanceModelMapper(
            instance(mockSweepstakeManager),
            instance(mockSweepstakeEntrySetManager),
            instance(mockSweepstakeEntrySetCodeGenerator),
            instance(mockSweepstakeGameBuyInBalanceCache)
        );
    }

    beforeEach(() => {
        reset(mockSweepstakeManager);
        reset(mockSweepstakeEntrySetManager);
        reset(mockSweepstakeEntrySetCodeGenerator);
        reset(mockSweepstakeGameBuyInBalanceCache);
    });

    describe('fromBalance()', () => {
        it('should return undefined if sweepstake is not found', async () => {
            // Given
            const balance: SweepstakeGameBuyInBalance = {
                sweepstakeId: 1,
                userId: 1,
                amountBase: new BigNumber(100),
                entryCount: 0,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const mapper = getMapper();

            when(mockSweepstakeManager.get(balance.sweepstakeId)).thenResolve(undefined);

            // When
            const result = await mapper.fromBalance(balance, undefined);

            // Then
            verify(mockSweepstakeManager.get(balance.sweepstakeId)).once();
            expect(result).to.be.undefined;
        });

        it('should return undefined if sweepstake ref does not match balance', async () => {
            // Given
            const balance: SweepstakeGameBuyInBalance = {
                sweepstakeId: 1,
                userId: 1,
                amountBase: new BigNumber(100),
                entryCount: 0,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const sweepstake = {
                id: 2
            } as Sweepstake;

            const mapper = getMapper();

            // When
            const result = await mapper.fromBalance(balance, sweepstake);

            // Then
            verify(mockSweepstakeManager.get(balance.sweepstakeId)).never();
            expect(result).to.be.undefined;
        });

        it('should return undefined if sweepstake does not have target metadata', async () => {
            // Given
            const balance: SweepstakeGameBuyInBalance = {
                sweepstakeId: 1,
                userId: 1,
                amountBase: new BigNumber(100),
                entryCount: 0,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const sweepstake = {
                id: 1
            } as Sweepstake;

            const mapper = getMapper();

            when(mockSweepstakeManager.get(balance.sweepstakeId)).thenResolve(sweepstake);

            // When
            const result = await mapper.fromBalance(balance, undefined);

            // Then
            verify(mockSweepstakeManager.get(balance.sweepstakeId)).once();
            expect(result).to.be.undefined;
        });

        it('should return model', async () => {
            // Given
            const balance: SweepstakeGameBuyInBalance = {
                sweepstakeId: 1,
                userId: 1,
                amountBase: new BigNumber(50),
                entryCount: 0,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 100,
                gameBuyInEntryMultiplier: 1
            };

            const sweepstake = {
                id: 1,
                metadata
            } as Sweepstake;

            const expected: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: balance.userId,
                currencyCode: Config.baseCurrency,
                amount: balance.amountBase,
                entryCount: balance.entryCount,
                entries: undefined,
                target: {
                    value: new BigNumber(metadata.gameBuyInEntryTarget),
                    multiplier: metadata.gameBuyInEntryMultiplier,
                    remaining: new BigNumber(50),
                    progress: 50,
                }
            };

            const mapper = getMapper();

            // When
            const result = await mapper.fromBalance(balance, sweepstake);

            // Then
            verify(mockSweepstakeManager.get(balance.sweepstakeId)).never();
            expect(result).to.deep.equal(expected);
        });

        it('should return model for balance with entries already awarded', async () => {
            // Given
            const balance: SweepstakeGameBuyInBalance = {
                sweepstakeId: 1,
                userId: 1,
                amountBase: new BigNumber(120),
                entryCount: 1,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 100,
                gameBuyInEntryMultiplier: 1
            };

            const sweepstake = {
                id: 1,
                metadata
            } as Sweepstake;

            const expected: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: balance.userId,
                currencyCode: Config.baseCurrency,
                amount: balance.amountBase,
                entryCount: balance.entryCount,
                entries: undefined,
                target: {
                    value: new BigNumber(metadata.gameBuyInEntryTarget),
                    multiplier: metadata.gameBuyInEntryMultiplier,
                    remaining: new BigNumber(80),
                    progress: 20,
                }
            };

            const mapper = getMapper();

            // When
            const result = await mapper.fromBalance(balance, sweepstake);

            // Then
            verify(mockSweepstakeManager.get(balance.sweepstakeId)).never();
            expect(result).to.deep.equal(expected);
        });

        it('should return model for balance with entry multiplier', async () => {
            // Given
            const balance: SweepstakeGameBuyInBalance = {
                sweepstakeId: 1,
                userId: 1,
                amountBase: new BigNumber(120),
                entryCount: 10,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 100,
                gameBuyInEntryMultiplier: 10
            };

            const sweepstake = {
                id: 1,
                metadata
            } as Sweepstake;

            const expected: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: balance.userId,
                currencyCode: Config.baseCurrency,
                amount: balance.amountBase,
                entryCount: balance.entryCount,
                entries: undefined,
                target: {
                    value: new BigNumber(metadata.gameBuyInEntryTarget),
                    multiplier: metadata.gameBuyInEntryMultiplier,
                    remaining: new BigNumber(80),
                    progress: 20,
                }
            };

            const mapper = getMapper();

            // When
            const result = await mapper.fromBalance(balance, sweepstake);

            // Then
            verify(mockSweepstakeManager.get(balance.sweepstakeId)).never();
            expect(result).to.deep.equal(expected);
        });

        it('should return model for uneven balance with entry multiplier', async () => {
            // Given
            const balance: SweepstakeGameBuyInBalance = {
                sweepstakeId: 1,
                userId: 1,
                amountBase: new BigNumber(522.22),
                entryCount: 50,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 100,
                gameBuyInEntryMultiplier: 10
            };

            const sweepstake = {
                id: 1,
                metadata
            } as Sweepstake;

            const expected: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: balance.userId,
                currencyCode: Config.baseCurrency,
                amount: balance.amountBase,
                entryCount: balance.entryCount,
                entries: undefined,
                target: {
                    value: new BigNumber(metadata.gameBuyInEntryTarget),
                    multiplier: metadata.gameBuyInEntryMultiplier,
                    remaining: new BigNumber(77.78),
                    progress: 22,
                }
            };

            const mapper = getMapper();

            // When
            const result = await mapper.fromBalance(balance, sweepstake);

            // Then
            verify(mockSweepstakeManager.get(balance.sweepstakeId)).never();
            expect(result).to.deep.equal(expected);
        });

        it('should return model with capped values', async () => {
            // Given
            const balance: SweepstakeGameBuyInBalance = {
                sweepstakeId: 1,
                userId: 1,
                amountBase: new BigNumber(220),
                entryCount: 1,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 100,
                gameBuyInEntryMultiplier: 1
            };

            const sweepstake = {
                id: 1,
                metadata
            } as Sweepstake;

            const expected: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: balance.userId,
                currencyCode: Config.baseCurrency,
                amount: balance.amountBase,
                entryCount: balance.entryCount,
                entries: undefined,
                target: {
                    value: new BigNumber(metadata.gameBuyInEntryTarget),
                    multiplier: metadata.gameBuyInEntryMultiplier,
                    remaining: new BigNumber(0),
                    progress: 100,
                }
            };

            const mapper = getMapper();

            // When
            const result = await mapper.fromBalance(balance, sweepstake);

            // Then
            verify(mockSweepstakeManager.get(balance.sweepstakeId)).never();
            expect(result).to.deep.equal(expected);
        });

        it('should return model with entries', async () => {
            // Given
            const sweepstakeId = 1;
            const userId = 1;

            const balance: SweepstakeGameBuyInBalance = {
                sweepstakeId,
                userId,
                amountBase: new BigNumber(100),
                entryCount: 1,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 100,
                gameBuyInEntryMultiplier: 1
            };

            const sweepstake = {
                id: 1,
                metadata
            } as Sweepstake;

            const entrySet = {
                id: 1,
                count: 1,
            } as SweepstakeEntrySet;

            when(mockSweepstakeManager.get(sweepstakeId)).thenResolve(sweepstake);
            when(mockSweepstakeGameBuyInBalanceCache.get(sweepstakeId, userId)).thenResolve(balance);
            when(mockSweepstakeEntrySetManager.get(balance.sweepstakeId, balance.userId)).thenResolve([entrySet]);
            when(mockSweepstakeEntrySetCodeGenerator.generate(entrySet.id, entrySet.count)).thenReturn('CODE');

            const expected: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: balance.userId,
                currencyCode: Config.baseCurrency,
                amount: balance.amountBase,
                entryCount: balance.entryCount,
                entries: [{
                    id: entrySet.id,
                    code: 'CODE',
                    count: entrySet.count
                }],
                target: {
                    value: new BigNumber(metadata.gameBuyInEntryTarget),
                    multiplier: metadata.gameBuyInEntryMultiplier,
                    remaining: new BigNumber(100),
                    progress: 0,
                }
            };

            const mapper = getMapper();

            // When
            const result = await mapper.from(sweepstakeId, userId);

            // Then
            verify(mockSweepstakeManager.get(balance.sweepstakeId)).once();
            verify(mockSweepstakeGameBuyInBalanceCache.get(sweepstakeId, userId)).once();
            verify(mockSweepstakeEntrySetManager.get(balance.sweepstakeId, balance.userId)).once();
            verify(mockSweepstakeEntrySetCodeGenerator.generate(entrySet.id, entrySet.count)).once();
            expect(result).to.deep.equal(expected);
        });

        it('should return default model if balance no found', async () => {
            // Given
            const sweepstakeId = 1;
            const userId = 1;

            const balance: SweepstakeGameBuyInBalance = {
                sweepstakeId,
                userId,
                amountBase: new BigNumber(0),
                entryCount: 0,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 100,
                gameBuyInEntryMultiplier: 1
            };

            const sweepstake = {
                id: 1,
                metadata,
                drawTime: anything(),
                closeTime: anything()
            } as Sweepstake;

            when(mockSweepstakeManager.get(sweepstakeId)).thenResolve(sweepstake);
            when(mockSweepstakeGameBuyInBalanceCache.get(sweepstakeId, userId)).thenResolve(undefined);

            const expected: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: balance.userId,
                currencyCode: Config.baseCurrency,
                amount: balance.amountBase,
                entryCount: balance.entryCount,
                target: {
                    value: new BigNumber(metadata.gameBuyInEntryTarget),
                    multiplier: metadata.gameBuyInEntryMultiplier,
                    remaining: new BigNumber(100),
                    progress: 0,
                }
            };

            const mapper = getMapper();

            // When
            const result = await mapper.from(sweepstakeId, userId);

            // Then
            verify(mockSweepstakeManager.get(balance.sweepstakeId)).once();
            verify(mockSweepstakeGameBuyInBalanceCache.get(sweepstakeId, userId)).once();
            expect(result).to.deep.equal(expected);
        });
    });
});