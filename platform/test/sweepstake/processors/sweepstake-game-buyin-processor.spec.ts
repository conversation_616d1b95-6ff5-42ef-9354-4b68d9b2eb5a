import { describe, it } from '@tcom/test';
import { anything, deepEqual, instance, mock, reset, verify, when } from '@tcom/test/mock';
import { BigNumber, Config } from '../../../src/core';
import { MessageBuilder } from '../../../src/websocket/message-builder';
import { Websocket } from '../../../src/websocket';
import { Game, GameManager, GameProvider, GameType } from '../../../src/game';
import { NewSweepstakeEntrySet, SweepstakeEntrySet, SweepstakeEntryState, SweepstakeGameBuyInBalanceManager, SweepstakeManager, SweepstakeMetadata, SweepstakeParticipantManager, SweepstakeState, SweepstakeType } from '../../../src/sweepstake';
import { ActiveSweepstake, SweepstakeActiveFilter, SweepstakeGameBuyInBalance, SweepstakeGameBuyInEventData } from '../../../src/sweepstake';
import { SweepstakeGameBuyInProcessor, SweepstakeGameBuyInNFTRequirementProcessor } from '../../../src/sweepstake/processors';
import { SweepstakeEntrySetCache, SweepstakeGameBuyInBalanceCache } from '../../../src/sweepstake/cache';
import { SweepstakeEligibilityProcessor, SweepstakeEligibilityProcessorResult } from '../../../src/sweepstake/eligibility';
import { SweepstakeGameBuyInBalanceModelMapper } from '../../../src/sweepstake/models/mappers';
import { SweepstakeGameBuyInBalanceModel } from '../../../src/sweepstake/models/sweepstake-game-buyin-balance.model';
import { SweepstakeEntrySetRepository } from '../../../src/sweepstake/repositories';
import { PlatformEventDispatcher } from '../../../src/core/events';
import { SweepstakeCacheExpiryMapper } from '../../../src/sweepstake/cache/mappers';
import { SweepstakeEntrySetAddedEvent } from '../../../src/sweepstake/events';
import { BlockchainNetwork } from '../../../src/blockchain';
import { User, UserManager, UserType } from '../../../src/user';

describe('SweepstakeGameBuyInProcessor', () => {
    const mockSweepstakeManager = mock(SweepstakeManager);
    const mockSweepstakeParticipantManager = mock(SweepstakeParticipantManager);
    const mockSweepstakeEntrySetCache = mock(SweepstakeEntrySetCache);
    const mockSweepstakeEntrySetRepository = mock(SweepstakeEntrySetRepository);
    const mockSweepstakeCacheExpiryMapper = mock(SweepstakeCacheExpiryMapper);
    const mockGameManager = mock(GameManager);
    const mockSweepstakeGameBuyInBalanceCache = mock(SweepstakeGameBuyInBalanceCache);
    const mockSweepstakeEligibilityProcessor = mock(SweepstakeEligibilityProcessor);
    const mockSweepstakeGameBuyInBalanceModelMapper = mock(SweepstakeGameBuyInBalanceModelMapper);
    const mockPlatformEventDispatcher = mock(PlatformEventDispatcher);
    const mockWebsocket = mock(Websocket);
    const mockNFTRequirementProcessor = mock(SweepstakeGameBuyInNFTRequirementProcessor);
    const mockSweepstakeGameBuyInBalanceManager = mock(SweepstakeGameBuyInBalanceManager);
    const mockUserManager = mock(UserManager);

    function getProcessor(): SweepstakeGameBuyInProcessor {
        return new SweepstakeGameBuyInProcessor(
            instance(mockSweepstakeManager),
            instance(mockSweepstakeParticipantManager),
            instance(mockSweepstakeEntrySetCache),
            instance(mockSweepstakeEntrySetRepository),
            instance(mockSweepstakeCacheExpiryMapper),
            instance(mockGameManager),
            instance(mockSweepstakeGameBuyInBalanceCache),
            instance(mockSweepstakeEligibilityProcessor),
            instance(mockSweepstakeGameBuyInBalanceModelMapper),
            instance(mockPlatformEventDispatcher),
            instance(mockWebsocket),
            instance(mockNFTRequirementProcessor),
            instance(mockSweepstakeGameBuyInBalanceManager),
            instance(mockUserManager)
        );
    }

    interface GameBuyInData {
        date?: Date;
        userId?: number;
        gameId?: number;
        gameProvider?: GameProvider;
        rollback?: boolean;
        amount?: BigNumber;
        amountBase?: BigNumber;
        effectiveAmount?: BigNumber;
        effectiveAmountBase?: BigNumber;
    }

    function getGameBuyInData(input?: GameBuyInData): SweepstakeGameBuyInEventData {
        return {
            date: input?.date ?? new Date(2024, 1, 1),
            userId: input?.userId ?? 1,
            gameId: input?.gameId ?? 1,
            gameProvider: GameProvider[input?.gameProvider ?? GameProvider.Playtech],
            currencyCode: Config.baseCurrency,
            amountBase: input?.amountBase ?? new BigNumber(10),
            effectiveAmountBase: input?.effectiveAmountBase ?? new BigNumber(10),
            rollback: input?.rollback ?? false
        };
    }

    beforeEach(() => {
        reset(mockSweepstakeManager);
        reset(mockSweepstakeParticipantManager);
        reset(mockSweepstakeEntrySetCache);
        reset(mockSweepstakeEntrySetRepository);
        reset(mockSweepstakeCacheExpiryMapper);
        reset(mockGameManager);
        reset(mockSweepstakeGameBuyInBalanceCache);
        reset(mockSweepstakeEligibilityProcessor);
        reset(mockSweepstakeGameBuyInBalanceModelMapper);
        reset(mockPlatformEventDispatcher);
        reset(mockWebsocket);
        reset(mockSweepstakeGameBuyInBalanceManager);
        reset(mockNFTRequirementProcessor);
        reset(mockUserManager);
        // Default: NFT requirement processor always passes unless overridden in test
        when(mockNFTRequirementProcessor.process(anything(), anything())).thenResolve({ passed: true });
    });

    describe('process()', () => {
        it('should stop processing if no sweepstakes are found', async () => {
            // Given
            const data = getGameBuyInData();
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve([]);

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).never();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
        });

        it('should stop processing if no applicable sweepstakes are found', async () => {
            // Given
            const data = getGameBuyInData();
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const sweepstakes = [{ id: 1 }] as ActiveSweepstake[];

            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).never();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
        });

        it('should stop processing if game is not found', async () => {
            // Given
            const data = getGameBuyInData();
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const processor = getProcessor();

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 1000,
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                metadata
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];

            when(mockGameManager.get(data.gameId)).thenResolve(undefined);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(anything())).once();
            verify(mockSweepstakeEligibilityProcessor.process(anything(), anything())).never();
        });

        it('should stop processing if effectiveAmountBase is zero', async () => {
            // Given
            const data = getGameBuyInData({ effectiveAmountBase: new BigNumber(0) });
            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).never();
            verify(mockSweepstakeManager.getAllActive(anything())).never();
            verify(mockSweepstakeEligibilityProcessor.process(anything(), anything())).never();
        });

        it('should stop processing if sweepstake is private and user is standard', async () => {
            // Given
            const data = getGameBuyInData();
            const game = { id: 1, type: GameType.Crash } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 1000,
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                metadata,
                public: false
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];
            const user = { type: UserType.Standard } as User;

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
            when(mockUserManager.get(data.userId)).thenResolve(user);

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockUserManager.get(data.userId)).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).never();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).never();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).never();
        });

        it('should stop processing if user is not eligible for sweepstake', async () => {
            // Given
            const data = getGameBuyInData();
            const game = { id: 1 } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 1000,
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                metadata,
                public: true
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];
            const user = { type: UserType.Standard } as User;
            const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                passed: false,
                evaluations: [],
            };

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
            when(mockUserManager.get(data.userId)).thenResolve(user);
            when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
        });

        it('should stop processing if game is excluded', async () => {
            // Given
            const data = getGameBuyInData();
            const game = { id: 1 } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 1000,
                excludedGameIds: [1],
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                metadata
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).never();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).never();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).never();
        });

        it('should stop processing if game is not included', async () => {
            // Given
            const data = getGameBuyInData();
            const game = { id: 1 } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 1000,
                includedGameIds: [2],
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                metadata
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).never();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).never();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).never();
        });

        it('should stop processing if game type is excluded', async () => {
            // Given
            const data = getGameBuyInData();
            const game = { id: 1, type: GameType.Hilo } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 1000,
                excludedGameTypes: [GameType.Hilo],
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                metadata
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).never();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).never();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).never();
        });

        it('should stop processing if game type is not included', async () => {
            // Given
            const data = getGameBuyInData();
            const game = { id: 1, type: GameType.Crash } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 1000,
                includedGameTypes: [GameType.Hilo],
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                metadata
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).never();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).never();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).never();
        });

        it('should stop processing if sweepstake is private and user is standard', async () => {
            // Given
            const data = getGameBuyInData();
            const game = { id: 1, type: GameType.Crash } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 1000,
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                metadata
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];
            const user = { type: UserType.Standard } as User;

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
            when(mockUserManager.get(data.userId)).thenResolve(user);

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockUserManager.get(data.userId)).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).never();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).never();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).never();
        });

        it('should stop processing if a rollback is received before game buyin balance is created', async () => {
            // Given
            const data = getGameBuyInData({ rollback: true });
            const game = { id: 1 } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 1000,
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                metadata,
                public: true
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];
            const user = { type: UserType.Standard } as User;

            const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                passed: true,
                evaluations: [],
            };

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
            when(mockUserManager.get(data.userId)).thenResolve(user);
            when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);
            when(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).thenCall(async (_sweepstakeId, _userId, callback) => { callback(); });
            when(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).thenResolve(undefined);

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockUserManager.get(data.userId)).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).once();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).once();
        });

        it('should process rollback', async () => {
            // Given
            const data = getGameBuyInData({ rollback: true });
            const game = { id: 1 } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 1000,
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                metadata,
                public: true
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];
            const user = { type: UserType.Standard } as User;

            const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                passed: true,
                evaluations: [],
            };

            const balance: SweepstakeGameBuyInBalance = {
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(100),
                entryCount: 0,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const balanceUpdate: SweepstakeGameBuyInBalance = {
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(90),
                entryCount: 0,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const balanceModel: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: data.userId,
                currencyCode: Config.baseCurrency,
                amount: new BigNumber(90),
                entryCount: 0,
                target: {
                    value: new BigNumber(1000),
                    multiplier: 1,
                    remaining: new BigNumber(910),
                    progress: 9
                }
            };

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
            when(mockUserManager.get(data.userId)).thenResolve(user);
            when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);

            when(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything()))
                .thenCall(async (_sweepstakeId, _userId, callback) => { return callback(); })
                .thenResolve(balance);

            when(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).thenResolve(balance);
            when(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, false)).thenResolve();
            when(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).thenResolve(balanceModel);

            const mockMessageBuilder = mock(MessageBuilder);
            when(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.payload(deepEqual(balanceModel))).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.send()).thenResolve();
            when(mockWebsocket.user(data.userId)).thenReturn(instance(mockMessageBuilder));

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockUserManager.get(data.userId)).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).once();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).once();
            verify(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, false)).once();
            verify(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).once();
            verify(mockWebsocket.user(data.userId)).once();
            verify(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).once();
            verify(mockMessageBuilder.payload(deepEqual(balanceModel))).once();
            verify(mockMessageBuilder.send()).once();
        });

        it('should process buyin and create balance', async () => {
            // Given
            const data = getGameBuyInData();
            const game = { id: 1 } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 1000,
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                metadata,
                closeTime: new Date(),
                public: true
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];
            const user = { type: UserType.Standard } as User;

            const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                passed: true,
                evaluations: [],
            };

            const balanceUpdate: SweepstakeGameBuyInBalance = {
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(10),
                entryCount: 0,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const balanceModel: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: data.userId,
                currencyCode: Config.baseCurrency,
                amount: new BigNumber(10),
                entryCount: 0,
                target: {
                    value: new BigNumber(1000),
                    multiplier: 1,
                    remaining: new BigNumber(990),
                    progress: 1
                }
            };

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
            when(mockUserManager.get(data.userId)).thenResolve(user);
            when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);

            when(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything()))
                .thenCall(async (_sweepstakeId, _userId, callback) => {
                    return callback();
                });

            when(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).thenResolve(undefined);
            when(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, true)).thenResolve();
            when(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).thenResolve(balanceModel);

            const mockMessageBuilder = mock(MessageBuilder);
            when(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.payload(deepEqual(balanceModel))).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.send()).thenResolve();
            when(mockWebsocket.user(data.userId)).thenReturn(instance(mockMessageBuilder));

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockUserManager.get(data.userId)).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).once();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).once();
            verify(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, true)).once();
            verify(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).once();
            verify(mockWebsocket.user(data.userId)).once();
            verify(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).once();
            verify(mockMessageBuilder.payload(deepEqual(balanceModel))).once();
            verify(mockMessageBuilder.send()).once();
        });

        it('should process buyin, create balance and award sweepstake entries', async () => {
            // Given
            const data = getGameBuyInData({
                effectiveAmountBase: new BigNumber(100)
            });

            const game = { id: 1 } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 100,
                gameBuyInEntryMultiplier: 5,
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                metadata,
                closeTime: new Date(),
                public: true
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];
            const user = { type: UserType.Standard } as User;

            const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                passed: true,
                evaluations: [],
            };

            const balanceUpdate: SweepstakeGameBuyInBalance = {
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(100),
                entryCount: 5,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const balanceModel: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: data.userId,
                currencyCode: Config.baseCurrency,
                amount: new BigNumber(100),
                entryCount: 5,
                target: {
                    value: new BigNumber(100),
                    multiplier: 5,
                    remaining: new BigNumber(0),
                    progress: 100
                }
            };

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
            when(mockUserManager.get(data.userId)).thenResolve(user);
            when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);

            when(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything()))
                .thenCall(async (_sweepstakeId, _userId, callback) => { return callback(); });

            when(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).thenResolve(undefined);
            when(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, true)).thenResolve();
            when(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).thenResolve(balanceModel);

            const mockMessageBuilder = mock(MessageBuilder);
            when(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.payload(deepEqual(balanceModel))).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.send()).thenResolve();
            when(mockWebsocket.user(data.userId)).thenReturn(instance(mockMessageBuilder));

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockUserManager.get(data.userId)).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).once();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).once();
            verify(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, true)).once();
            verify(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).once();
            verify(mockWebsocket.user(data.userId)).once();
            verify(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).once();
            verify(mockMessageBuilder.payload(deepEqual(balanceModel))).once();
            verify(mockMessageBuilder.send()).once();
        });

        it('should process buyin and update balance', async () => {
            // Given
            const data = getGameBuyInData();
            const game = { id: 1 } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 1000,
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                metadata,
                closeTime: new Date(),
                currencyCode: Config.baseCurrency,
                public: true
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];
            const user = { type: UserType.Standard } as User;

            const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                passed: true,
                evaluations: [],
            };

            const balance: SweepstakeGameBuyInBalance = {
                ...data,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(100),
                entryCount: 0,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const balanceUpdate: SweepstakeGameBuyInBalance = {
                ...data,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(110),
                entryCount: 0,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const balanceModel: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: data.userId,
                currencyCode: Config.baseCurrency,
                amount: new BigNumber(10),
                entryCount: 0,
                target: {
                    value: new BigNumber(1000),
                    multiplier: 1,
                    remaining: new BigNumber(990),
                    progress: 1
                }
            };

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
            when(mockUserManager.get(data.userId)).thenResolve(user);
            when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);

            when(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything()))
                .thenCall(async (_sweepstakeId, _userId, callback) => { return callback(); });

            when(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).thenResolve(balance);
            when(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, false)).thenResolve();
            when(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).thenResolve(balanceModel);

            const mockMessageBuilder = mock(MessageBuilder);
            when(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.payload(deepEqual(balanceModel))).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.send()).thenResolve();
            when(mockWebsocket.user(data.userId)).thenReturn(instance(mockMessageBuilder));

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockUserManager.get(data.userId)).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).once();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).once();
            verify(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, false)).once();
            verify(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).once();
            verify(mockWebsocket.user(data.userId)).once();
            verify(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).once();
            verify(mockMessageBuilder.payload(deepEqual(balanceModel))).once();
            verify(mockMessageBuilder.send()).once();
        });

        it('should process buyin, update balance and award sweepstake entries', async () => {
            // Given
            const data = getGameBuyInData();
            const game = { id: 1 } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 100,
                gameBuyInEntryMultiplier: 5,
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                state: SweepstakeState.Open,
                metadata,
                closeTime: new Date(),
                currencyCode: Config.baseCurrency,
                public: true
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];
            const user = { type: UserType.Standard } as User;

            const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                passed: true,
                evaluations: [],
            };

            const balance: SweepstakeGameBuyInBalance = {
                ...data,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(100),
                entryCount: 0,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const entryCount = 5;

            const balanceUpdate: SweepstakeGameBuyInBalance = {
                ...data,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(110),
                entryCount,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const balanceModel: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: data.userId,
                currencyCode: Config.baseCurrency,
                amount: new BigNumber(10),
                entryCount,
                target: {
                    value: new BigNumber(100),
                    multiplier: 5,
                    remaining: new BigNumber(0),
                    progress: 100
                }
            };

            const newEntrySet: NewSweepstakeEntrySet = {
                state: SweepstakeEntryState.Confirmed,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                count: entryCount,
                sourceCurrency: Config.defaultCurrency
            };

            const entrySet = {
                id: 1,
                count: entryCount,
                cost: new BigNumber(0),
                state: SweepstakeEntryState.Confirmed,
                createTime: anything()
            } as SweepstakeEntrySet;

            const expiryTime = anything();
            const event = new SweepstakeEntrySetAddedEvent({
                sweepstakeId: sweepstake.id,
                sweepstakeType: sweepstake.type,
                sweepstakePresentationType: sweepstake.presentationType,
                sweepstakeMechanism: sweepstake.mechanism,
                sweepstakeCurrencyCode: sweepstake.currencyCode,
                userId: data.userId,
                currencyCode: Config.defaultCurrency,
                entrySetId: entrySet.id,
                entryCount: entrySet.count,
                cost: entrySet.cost,
                createTime: entrySet.createTime
            });

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
            when(mockUserManager.get(data.userId)).thenResolve(user);
            when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);

            when(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything()))
                .thenCall(async (_sweepstakeId, _userId, callback) => { return callback(); });

            when(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).thenResolve(balance);
            when(mockSweepstakeEntrySetRepository.add(deepEqual(newEntrySet))).thenResolve(entrySet);
            when(mockSweepstakeCacheExpiryMapper.from(deepEqual(sweepstake))).thenResolve(expiryTime);
            when(mockSweepstakeEntrySetCache.upsert(deepEqual(entrySet), expiryTime)).thenResolve();
            when(mockSweepstakeParticipantManager.addOrUpdate(deepEqual(sweepstake), data.userId)).thenResolve();
            when(mockPlatformEventDispatcher.send(deepEqual(event))).thenResolve();

            when(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, false)).thenResolve();
            when(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).thenResolve(balanceModel);

            const mockMessageBuilder = mock(MessageBuilder);
            when(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.payload(deepEqual(balanceModel))).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.send()).thenResolve();
            when(mockWebsocket.user(data.userId)).thenReturn(instance(mockMessageBuilder));

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockUserManager.get(data.userId)).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).once();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).once();
            verify(mockSweepstakeEntrySetRepository.add(deepEqual(newEntrySet))).once();
            verify(mockSweepstakeEntrySetCache.upsert(deepEqual(entrySet), expiryTime)).once();
            verify(mockSweepstakeParticipantManager.addOrUpdate(deepEqual(sweepstake), data.userId)).once();
            verify(mockPlatformEventDispatcher.send(anything())).once();
            verify(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, false)).once();
            verify(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).once();
            verify(mockWebsocket.user(data.userId)).once();
            verify(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).once();
            verify(mockMessageBuilder.payload(deepEqual(balanceModel))).once();
            verify(mockMessageBuilder.send()).once();
        });

        it('should process buyin, update balance and increment sweepstake entries', async () => {
            // Given
            const data = getGameBuyInData();
            const game = { id: 1 } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 100,
                gameBuyInEntryMultiplier: 5,
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                state: SweepstakeState.Open,
                metadata,
                closeTime: new Date(),
                currencyCode: Config.baseCurrency,
                public: true
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];
            const user = { type: UserType.Standard } as User;

            const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                passed: true,
                evaluations: [],
            };

            const balance: SweepstakeGameBuyInBalance = {
                ...data,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(100),
                entryCount: 3,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const balanceUpdate: SweepstakeGameBuyInBalance = {
                ...data,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(110),
                entryCount: 5,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const balanceModel: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: data.userId,
                currencyCode: Config.baseCurrency,
                amount: new BigNumber(10),
                entryCount: 5,
                target: {
                    value: new BigNumber(100),
                    multiplier: 5,
                    remaining: new BigNumber(0),
                    progress: 100
                }
            };

            const newEntrySet: NewSweepstakeEntrySet = {
                state: SweepstakeEntryState.Confirmed,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                count: 2,
                sourceCurrency: Config.defaultCurrency
            };

            const entrySet = {
                id: 1,
                count: 2,
                cost: new BigNumber(0),
                state: SweepstakeEntryState.Confirmed,
                createTime: anything()
            } as SweepstakeEntrySet;

            const expiryTime = anything();
            const event = new SweepstakeEntrySetAddedEvent({
                sweepstakeId: sweepstake.id,
                sweepstakeType: sweepstake.type,
                sweepstakePresentationType: sweepstake.presentationType,
                sweepstakeMechanism: sweepstake.mechanism,
                sweepstakeCurrencyCode: sweepstake.currencyCode,
                userId: data.userId,
                currencyCode: Config.defaultCurrency,
                entrySetId: entrySet.id,
                entryCount: entrySet.count,
                cost: entrySet.cost,
                createTime: entrySet.createTime
            });

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
            when(mockUserManager.get(data.userId)).thenResolve(user);
            when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);

            when(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything()))
                .thenCall(async (_sweepstakeId, _userId, callback) => { return callback(); });

            when(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).thenResolve(balance);

            when(mockSweepstakeEntrySetRepository.add(deepEqual(newEntrySet))).thenResolve(entrySet);
            when(mockSweepstakeCacheExpiryMapper.from(deepEqual(sweepstake))).thenResolve(expiryTime);
            when(mockSweepstakeEntrySetCache.upsert(deepEqual(entrySet), expiryTime)).thenResolve();
            when(mockSweepstakeParticipantManager.addOrUpdate(deepEqual(sweepstake), data.userId)).thenResolve();
            when(mockPlatformEventDispatcher.send(deepEqual(event))).thenResolve();
            when(mockSweepstakeGameBuyInBalanceManager.update(deepEqual(balanceUpdate))).thenResolve();
            when(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).thenResolve(balanceModel);

            const mockMessageBuilder = mock(MessageBuilder);
            when(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.payload(deepEqual(balanceModel))).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.send()).thenResolve();
            when(mockWebsocket.user(data.userId)).thenReturn(instance(mockMessageBuilder));

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockUserManager.get(data.userId)).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).once();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).once();
            verify(mockSweepstakeEntrySetRepository.add(deepEqual(newEntrySet))).once();
            verify(mockSweepstakeEntrySetCache.upsert(deepEqual(entrySet), expiryTime)).once();
            verify(mockSweepstakeParticipantManager.addOrUpdate(deepEqual(sweepstake), data.userId)).once();
            verify(mockPlatformEventDispatcher.send(anything())).once();
            verify(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, false)).once();
            verify(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).once();
            verify(mockWebsocket.user(data.userId)).once();
            verify(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).once();
            verify(mockMessageBuilder.payload(deepEqual(balanceModel))).once();
            verify(mockMessageBuilder.send()).once();
        });

        it('should process buyin, update balance and increment sweepstake entries with multiplier', async () => {
            // Given
            const data = getGameBuyInData({
                effectiveAmountBase: new BigNumber(100)
            });

            const game = { id: 1 } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 100,
                gameBuyInEntryMultiplier: 5,
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                state: SweepstakeState.Open,
                metadata,
                closeTime: new Date(),
                currencyCode: Config.baseCurrency,
                public: true
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];
            const user = { type: UserType.Standard } as User;

            const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                passed: true,
                evaluations: [],
            };

            const balance: SweepstakeGameBuyInBalance = {
                ...data,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(100),
                entryCount: 5,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const balanceUpdate: SweepstakeGameBuyInBalance = {
                ...data,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(200),
                entryCount: 10,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const balanceModel: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: data.userId,
                currencyCode: Config.baseCurrency,
                amount: new BigNumber(200),
                entryCount: 10,
                target: {
                    value: new BigNumber(100),
                    multiplier: 5,
                    remaining: new BigNumber(0),
                    progress: 100
                }
            };

            const newEntrySet: NewSweepstakeEntrySet = {
                state: SweepstakeEntryState.Confirmed,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                count: 5,
                sourceCurrency: Config.defaultCurrency
            };

            const entrySet = {
                id: 1,
                count: 5,
                cost: new BigNumber(0),
                state: SweepstakeEntryState.Confirmed,
                createTime: anything()
            } as SweepstakeEntrySet;

            const expiryTime = anything();
            const event = new SweepstakeEntrySetAddedEvent({
                sweepstakeId: sweepstake.id,
                sweepstakeType: sweepstake.type,
                sweepstakePresentationType: sweepstake.presentationType,
                sweepstakeMechanism: sweepstake.mechanism,
                sweepstakeCurrencyCode: sweepstake.currencyCode,
                userId: data.userId,
                currencyCode: Config.defaultCurrency,
                entrySetId: entrySet.id,
                entryCount: entrySet.count,
                cost: entrySet.cost,
                createTime: entrySet.createTime
            });

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
            when(mockUserManager.get(data.userId)).thenResolve(user);
            when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);

            when(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything()))
                .thenCall(async (_sweepstakeId, _userId, callback) => { return callback(); })
                .thenResolve(balance);

            when(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).thenResolve(balance);
            when(mockSweepstakeEntrySetRepository.add(deepEqual(newEntrySet))).thenResolve(entrySet);
            when(mockSweepstakeCacheExpiryMapper.from(deepEqual(sweepstake))).thenResolve(expiryTime);
            when(mockSweepstakeEntrySetCache.upsert(deepEqual(entrySet), expiryTime)).thenResolve();
            when(mockSweepstakeParticipantManager.addOrUpdate(deepEqual(sweepstake), data.userId)).thenResolve();
            when(mockPlatformEventDispatcher.send(deepEqual(event))).thenResolve();

            when(mockSweepstakeGameBuyInBalanceManager.update(deepEqual(balanceUpdate))).thenResolve();
            when(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).thenResolve(balanceModel);

            const mockMessageBuilder = mock(MessageBuilder);
            when(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.payload(deepEqual(balanceModel))).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.send()).thenResolve();
            when(mockWebsocket.user(data.userId)).thenReturn(instance(mockMessageBuilder));

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockUserManager.get(data.userId)).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).once();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).once();
            verify(mockSweepstakeEntrySetRepository.add(deepEqual(newEntrySet))).once();
            verify(mockSweepstakeEntrySetCache.upsert(deepEqual(entrySet), expiryTime)).once();
            verify(mockSweepstakeParticipantManager.addOrUpdate(deepEqual(sweepstake), data.userId)).once();
            verify(mockPlatformEventDispatcher.send(anything())).once();
            verify(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, false)).once();
            verify(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).once();
            verify(mockWebsocket.user(data.userId)).once();
            verify(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).once();
            verify(mockMessageBuilder.payload(deepEqual(balanceModel))).once();
            verify(mockMessageBuilder.send()).once();
        });

        it('should process buyin but cap entries at the max count', async () => {
            // Given
            const data = getGameBuyInData();

            const game = { id: 1 } as Game;
            const filter: SweepstakeActiveFilter = {
                type: SweepstakeType.Platform,
                states: [
                    SweepstakeState.Open
                ]
            };

            const metadata: SweepstakeMetadata = {
                gameBuyInEntryTarget: 100,
                gameBuyInMaxEntryCount: 5,
                gameBuyInWebsocket: true
            };

            const sweepstake = {
                id: 1,
                state: SweepstakeState.Open,
                metadata,
                closeTime: new Date(),
                currencyCode: Config.baseCurrency,
                public: true
            } as ActiveSweepstake;

            const sweepstakes = [sweepstake] as ActiveSweepstake[];
            const user = { type: UserType.Standard } as User;

            const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                passed: true,
                evaluations: [],
            };

            const balance: SweepstakeGameBuyInBalance = {
                ...data,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(1000),
                entryCount: 3,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const balanceUpdate: SweepstakeGameBuyInBalance = {
                ...data,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                amountBase: new BigNumber(1010),
                entryCount: 5,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            };

            const balanceModel: SweepstakeGameBuyInBalanceModel = {
                sweepstake: {
                    id: sweepstake.id,
                    name: sweepstake.name,
                    closeTime: sweepstake.closeTime,
                    drawTime: sweepstake.drawTime
                },
                userId: data.userId,
                currencyCode: Config.baseCurrency,
                amount: new BigNumber(1000),
                entryCount: 5,
                target: {
                    value: new BigNumber(100),
                    multiplier: 5,
                    remaining: new BigNumber(0),
                    progress: 100
                }
            };

            const newEntrySet: NewSweepstakeEntrySet = {
                state: SweepstakeEntryState.Confirmed,
                sweepstakeId: sweepstake.id,
                userId: data.userId,
                count: 2,
                sourceCurrency: Config.defaultCurrency
            };

            const entrySet = {
                id: 1,
                count: 2,
                cost: new BigNumber(0),
                state: SweepstakeEntryState.Confirmed,
                createTime: anything()
            } as SweepstakeEntrySet;

            const expiryTime = anything();
            const event = new SweepstakeEntrySetAddedEvent({
                sweepstakeId: sweepstake.id,
                sweepstakeType: sweepstake.type,
                sweepstakePresentationType: sweepstake.presentationType,
                sweepstakeMechanism: sweepstake.mechanism,
                sweepstakeCurrencyCode: sweepstake.currencyCode,
                userId: data.userId,
                currencyCode: Config.defaultCurrency,
                entrySetId: entrySet.id,
                entryCount: entrySet.count,
                cost: entrySet.cost,
                createTime: entrySet.createTime
            });

            when(mockGameManager.get(data.gameId)).thenResolve(game);
            when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
            when(mockUserManager.get(data.userId)).thenResolve(user);
            when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);
            when(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).thenCall(async (_sweepstakeId, _userId, callback) => { callback(); });
            when(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).thenResolve(balance);

            when(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything()))
                .thenCall(async (_sweepstakeId, _userId, callback) => { return callback(); })
                .thenResolve(balance);

            when(mockSweepstakeEntrySetRepository.add(deepEqual(newEntrySet))).thenResolve(entrySet);
            when(mockSweepstakeCacheExpiryMapper.from(deepEqual(sweepstake))).thenResolve(expiryTime);
            when(mockSweepstakeEntrySetCache.upsert(deepEqual(entrySet), expiryTime)).thenResolve();
            when(mockSweepstakeParticipantManager.addOrUpdate(deepEqual(sweepstake), data.userId)).thenResolve();
            when(mockPlatformEventDispatcher.send(deepEqual(event))).thenResolve();

            when(mockSweepstakeGameBuyInBalanceManager.update(deepEqual(balanceUpdate))).thenResolve();
            when(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).thenResolve(balanceModel);

            const mockMessageBuilder = mock(MessageBuilder);
            when(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.payload(deepEqual(balanceModel))).thenReturn(instance(mockMessageBuilder));
            when(mockMessageBuilder.send()).thenResolve();
            when(mockWebsocket.user(data.userId)).thenReturn(instance(mockMessageBuilder));

            const processor = getProcessor();

            // When
            await processor.process(data);

            // Then
            verify(mockGameManager.get(data.gameId)).once();
            verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
            verify(mockUserManager.get(data.userId)).once();
            verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
            verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).once();
            verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).once();
            verify(mockSweepstakeEntrySetRepository.add(deepEqual(newEntrySet))).once();
            verify(mockSweepstakeEntrySetCache.upsert(deepEqual(entrySet), expiryTime)).once();
            verify(mockSweepstakeParticipantManager.addOrUpdate(deepEqual(sweepstake), data.userId)).once();
            verify(mockPlatformEventDispatcher.send(anything())).once();
            verify(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, false)).once();
            verify(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).once();
            verify(mockWebsocket.user(data.userId)).once();
            verify(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).once();
            verify(mockMessageBuilder.payload(deepEqual(balanceModel))).once();
            verify(mockMessageBuilder.send()).once();
        });

        describe('NFT ownership requirements', () => {
            it('should process when nftHolderRequirements is not defined in the metadata', async () => {
                // Given
                const data = getGameBuyInData();
                const game = { id: 1 } as Game;
                const filter: SweepstakeActiveFilter = {
                    type: SweepstakeType.Platform,
                    states: [
                        SweepstakeState.Open
                    ]
                };

                const metadata: SweepstakeMetadata = {
                    gameBuyInEntryTarget: 1000,
                    gameBuyInWebsocket: true
                };

                const sweepstake = {
                    id: 1,
                    metadata,
                    closeTime: new Date(),
                    public: true
                } as ActiveSweepstake;

                const sweepstakes = [sweepstake] as ActiveSweepstake[];
                const user = { type: UserType.Standard } as User;

                const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                    passed: true,
                    evaluations: [],
                };

                const balanceUpdate: SweepstakeGameBuyInBalance = {
                    sweepstakeId: sweepstake.id,
                    userId: data.userId,
                    amountBase: new BigNumber(10),
                    entryCount: 0,
                    manualEntryAdditions: 0,
                    manualEntryDeductions: 0,
                    manualAmountBase: new BigNumber(0)
                };

                const balanceModel: SweepstakeGameBuyInBalanceModel = {
                    sweepstake: {
                        id: sweepstake.id,
                        name: sweepstake.name,
                        closeTime: sweepstake.closeTime,
                        drawTime: sweepstake.drawTime
                    },
                    userId: data.userId,
                    currencyCode: Config.baseCurrency,
                    amount: new BigNumber(10),
                    entryCount: 0,
                    target: {
                        value: new BigNumber(1000),
                        multiplier: 1,
                        remaining: new BigNumber(990),
                        progress: 1
                    }
                };

                when(mockGameManager.get(data.gameId)).thenResolve(game);
                when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
                when(mockUserManager.get(data.userId)).thenResolve(user);
                when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);
                when(mockNFTRequirementProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve({ passed: true });

                when(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything()))
                    .thenCall(async (_sweepstakeId, _userId, callback) => { return callback(); })
                    .thenResolve(balanceUpdate);

                when(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).thenResolve(undefined);
                when(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, true)).thenResolve();
                when(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).thenResolve(balanceModel);

                const mockMessageBuilder = mock(MessageBuilder);
                when(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).thenReturn(instance(mockMessageBuilder));
                when(mockMessageBuilder.payload(deepEqual(balanceModel))).thenReturn(instance(mockMessageBuilder));
                when(mockMessageBuilder.send()).thenResolve();
                when(mockWebsocket.user(data.userId)).thenReturn(instance(mockMessageBuilder));

                const processor = getProcessor();

                // When
                await processor.process(data);

                // Then
                verify(mockGameManager.get(data.gameId)).once();
                verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
                verify(mockUserManager.get(data.userId)).once();
                verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
                verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).once();
                verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).once();
                verify(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, true)).once();
                verify(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).once();
                verify(mockWebsocket.user(data.userId)).once();
                verify(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).once();
                verify(mockMessageBuilder.payload(deepEqual(balanceModel))).once();
                verify(mockMessageBuilder.send()).once();
            });

            it('should process when nftHolderRequirements is an empty array', async () => {
                // Given
                const data = getGameBuyInData();
                const game = { id: 1 } as Game;
                const filter: SweepstakeActiveFilter = {
                    type: SweepstakeType.Platform,
                    states: [
                        SweepstakeState.Open
                    ]
                };

                const metadata: SweepstakeMetadata = {
                    gameBuyInEntryTarget: 1000,
                    gameBuyInWebsocket: true,
                    nftHolderRequirements: []
                };

                const sweepstake = {
                    id: 1,
                    metadata,
                    closeTime: new Date(),
                    public: true
                } as ActiveSweepstake;

                const sweepstakes = [sweepstake] as ActiveSweepstake[];
                const user = { type: UserType.Standard } as User;

                const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                    passed: true,
                    evaluations: [],
                };

                const balanceUpdate: SweepstakeGameBuyInBalance = {
                    sweepstakeId: sweepstake.id,
                    userId: data.userId,
                    amountBase: new BigNumber(10),
                    entryCount: 0,
                    manualEntryAdditions: 0,
                    manualEntryDeductions: 0,
                    manualAmountBase: new BigNumber(0)
                };

                const balanceModel: SweepstakeGameBuyInBalanceModel = {
                    sweepstake: {
                        id: sweepstake.id,
                        name: sweepstake.name,
                        closeTime: sweepstake.closeTime,
                        drawTime: sweepstake.drawTime
                    },
                    userId: data.userId,
                    currencyCode: Config.baseCurrency,
                    amount: new BigNumber(10),
                    entryCount: 0,
                    target: {
                        value: new BigNumber(1000),
                        multiplier: 1,
                        remaining: new BigNumber(990),
                        progress: 1
                    }
                };

                when(mockGameManager.get(data.gameId)).thenResolve(game);
                when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
                when(mockUserManager.get(data.userId)).thenResolve(user);
                when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);
                when(mockNFTRequirementProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve({ passed: true });

                when(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything()))
                    .thenCall(async (_sweepstakeId, _userId, callback) => { return callback(); })
                    .thenResolve(balanceUpdate);

                when(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).thenResolve(undefined);
                when(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, true)).thenResolve();
                when(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).thenResolve(balanceModel);

                const mockMessageBuilder = mock(MessageBuilder);
                when(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).thenReturn(instance(mockMessageBuilder));
                when(mockMessageBuilder.payload(deepEqual(balanceModel))).thenReturn(instance(mockMessageBuilder));
                when(mockMessageBuilder.send()).thenResolve();
                when(mockWebsocket.user(data.userId)).thenReturn(instance(mockMessageBuilder));

                const processor = getProcessor();

                // When
                await processor.process(data);

                // Then
                verify(mockGameManager.get(data.gameId)).once();
                verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
                verify(mockUserManager.get(data.userId)).once();
                verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
                verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).once();
                verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).once();
                verify(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, true)).once();
                verify(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).once();
                verify(mockWebsocket.user(data.userId)).once();
                verify(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).once();
                verify(mockMessageBuilder.payload(deepEqual(balanceModel))).once();
                verify(mockMessageBuilder.send()).once();
                verify(mockNFTRequirementProcessor.process(anything(), anything())).once();
            });

            it('should process when user owns at least one NFT from the required collections', async () => {
                // Given
                const data = getGameBuyInData();
                const game = { id: 1 } as Game;
                const filter: SweepstakeActiveFilter = {
                    type: SweepstakeType.Platform,
                    states: [
                        SweepstakeState.Open
                    ]
                };

                const nftRequirements = [
                    {
                        contractAddress: '0x123456789abcdef',
                        network: BlockchainNetwork.Ethereum
                    },
                    {
                        contractAddress: '0xabcdef123456789',
                        network: BlockchainNetwork.Polygon
                    }
                ];

                const metadata: SweepstakeMetadata = {
                    gameBuyInEntryTarget: 1000,
                    gameBuyInWebsocket: true,
                    nftHolderRequirements: nftRequirements
                };

                const sweepstake = {
                    id: 1,
                    metadata,
                    closeTime: new Date(),
                    public: true
                } as ActiveSweepstake;

                const sweepstakes = [sweepstake] as ActiveSweepstake[];
                const user = { type: UserType.Standard } as User;

                const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                    passed: true,
                    evaluations: [],
                };

                const balanceUpdate: SweepstakeGameBuyInBalance = {
                    sweepstakeId: sweepstake.id,
                    userId: data.userId,
                    amountBase: new BigNumber(10),
                    entryCount: 0,
                    manualEntryAdditions: 0,
                    manualEntryDeductions: 0,
                    manualAmountBase: new BigNumber(0)
                };

                const balanceModel: SweepstakeGameBuyInBalanceModel = {
                    sweepstake: {
                        id: sweepstake.id,
                        name: sweepstake.name,
                        closeTime: sweepstake.closeTime,
                        drawTime: sweepstake.drawTime
                    },
                    userId: data.userId,
                    currencyCode: Config.baseCurrency,
                    amount: new BigNumber(10),
                    entryCount: 0,
                    target: {
                        value: new BigNumber(1000),
                        multiplier: 1,
                        remaining: new BigNumber(990),
                        progress: 1
                    }
                };

                when(mockGameManager.get(data.gameId)).thenResolve(game);
                when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
                when(mockUserManager.get(data.userId)).thenResolve(user);
                when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);
                when(mockNFTRequirementProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve({ passed: true });

                when(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything()))
                    .thenCall(async (_sweepstakeId, _userId, callback) => { return callback(); })
                    .thenResolve(balanceUpdate);

                when(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).thenResolve(undefined);
                when(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, true)).thenResolve();
                when(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).thenResolve(balanceModel);

                const mockMessageBuilder = mock(MessageBuilder);
                when(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).thenReturn(instance(mockMessageBuilder));
                when(mockMessageBuilder.payload(deepEqual(balanceModel))).thenReturn(instance(mockMessageBuilder));
                when(mockMessageBuilder.send()).thenResolve();
                when(mockWebsocket.user(data.userId)).thenReturn(instance(mockMessageBuilder));

                const processor = getProcessor();

                // When
                await processor.process(data);

                // Then
                verify(mockGameManager.get(data.gameId)).once();
                verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
                verify(mockUserManager.get(data.userId)).once();
                verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
                verify(mockNFTRequirementProcessor.process(deepEqual(sweepstake), data.userId)).once();
                verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).once();
                verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).once();
                verify(mockSweepstakeGameBuyInBalanceManager.upsert(deepEqual(balanceUpdate), sweepstake.closeTime, true)).once();
                verify(mockSweepstakeGameBuyInBalanceModelMapper.fromBalance(deepEqual(balanceUpdate), sweepstake)).once();
                verify(mockWebsocket.user(data.userId)).once();
                verify(mockMessageBuilder.type('Sweepstake:GameBuyIn:Balance:Change')).once();
                verify(mockMessageBuilder.payload(deepEqual(balanceModel))).once();
                verify(mockMessageBuilder.send()).once();
            });

            it('should stop processing when user does not own any NFTs from the required collections', async () => {
                // Given
                const data = getGameBuyInData();
                const game = { id: 1 } as Game;
                const filter: SweepstakeActiveFilter = {
                    type: SweepstakeType.Platform,
                    states: [
                        SweepstakeState.Open
                    ]
                };

                const nftRequirements = [
                    {
                        contractAddress: '0x123456789abcdef',
                        network: BlockchainNetwork.Ethereum
                    },
                    {
                        contractAddress: '0xabcdef123456789',
                        network: BlockchainNetwork.Polygon
                    }
                ];

                const metadata: SweepstakeMetadata = {
                    gameBuyInEntryTarget: 1000,
                    gameBuyInWebsocket: true,
                    nftHolderRequirements: nftRequirements
                };

                const sweepstake = {
                    id: 1,
                    metadata,
                    closeTime: new Date(),
                    public: true
                } as ActiveSweepstake;

                const sweepstakes = [sweepstake] as ActiveSweepstake[];
                const user = { type: UserType.Standard } as User;

                const eligibilityResult: SweepstakeEligibilityProcessorResult = {
                    passed: true,
                    evaluations: [],
                };

                when(mockGameManager.get(data.gameId)).thenResolve(game);
                when(mockSweepstakeManager.getAllActive(deepEqual(filter))).thenResolve(sweepstakes);
                when(mockUserManager.get(data.userId)).thenResolve(user);
                when(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve(eligibilityResult);
                when(mockNFTRequirementProcessor.process(deepEqual(sweepstake), data.userId)).thenResolve({ passed: false });

                const processor = getProcessor();

                // When
                await processor.process(data);

                // Then
                verify(mockGameManager.get(data.gameId)).once();
                verify(mockSweepstakeManager.getAllActive(deepEqual(filter))).once();
                verify(mockUserManager.get(data.userId)).once();
                verify(mockSweepstakeEligibilityProcessor.process(deepEqual(sweepstake), data.userId)).once();
                verify(mockNFTRequirementProcessor.process(deepEqual(sweepstake), data.userId)).once();
                verify(mockSweepstakeGameBuyInBalanceCache.lock(sweepstake.id, data.userId, anything())).never();
                verify(mockSweepstakeGameBuyInBalanceManager.get(sweepstake.id, data.userId)).never();
            });
        });
    });
});