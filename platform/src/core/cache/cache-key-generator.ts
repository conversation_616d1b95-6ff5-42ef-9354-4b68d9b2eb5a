export class CacheKeyGenerator {
    constructor(private readonly prefix: string) {
    }

    public generate(...params: any[]): string {
        let parameters = [this.prefix];

        params = params.filter(p => p !== undefined && p !== null);

        // Flatten object parameter recursively
        params = params.map(p => this.flatten(p)).flat();

        if (params.length > 0)
            parameters = parameters.concat(params);

        return parameters.join(':');
    }

    // Flatten with parameter names and values
    private flatten(obj: any): any[] {
        if (typeof obj !== 'object')
            return [obj];

        return Object.entries(obj).filter(([, value]) => !!value).map(([key, value]) => {
            if (typeof value !== 'object')
                return `${key}-${value}`;

            return this.flatten(value);
        }).flat();
    }
}