import { Inject, Singleton } from '../../core/ioc';
import Logger, { LogClass } from '../../core/logging';
import { BigNumber, Config } from '../../core';
import { PlatformEventDispatcher } from '../../core/events';
import { Websocket } from '../../websocket';
import { Game, GameManager } from '../../game';
import { ActiveSweepstake } from '../sweepstake-active';
import { SweepstakeEntrySetAddedEvent } from '../events';
import { SweepstakeState } from '../sweepstake-state';
import { SweepstakeManager } from '../sweepstake-manager';
import { SweepstakeType } from '../sweepstake-type';
import { SweepstakeEligibilityProcessor } from '../eligibility';
import { SweepstakeGameBuyInBalance, SweepstakeGameBuyInEventData } from '../sweepstake-game-buyin';
import { SweepstakeEntrySetCache, SweepstakeGameBuyInBalanceCache } from '../cache';
import { SweepstakeCacheExpiryMapper } from '../cache/mappers';
import { SweepstakeGameBuyInBalanceModelMapper } from '../models/mappers';
import { SweepstakeParticipantManager } from '../sweepstake-participant-manager';
import { SweepstakeEntrySetRepository } from '../repositories';
import { SweepstakeEntryState } from '../sweepstake-entry-state';
import { SweepstakeGameBuyInNFTRequirementProcessor } from './sweepstake-game-buyin-nft-requirement-processor';
import { SweepstakeGameBuyInBalanceManager } from '../sweepstake-game-buyin-balance-manager';
import { UserManager } from '../../user';
import { isAnonOrStandard } from '../../user/utilities';
import _ from 'lodash';

interface SweepstakeGameBuyInBalanceResult {
    balance: SweepstakeGameBuyInBalance;
    entryCount: number;
}

@Singleton
@LogClass()
export class SweepstakeGameBuyInProcessor {
    constructor(
        @Inject private readonly sweepstakeManager: SweepstakeManager,
        @Inject private readonly participantManager: SweepstakeParticipantManager,
        @Inject private readonly entrySetCache: SweepstakeEntrySetCache,
        @Inject private readonly entrySetRepository: SweepstakeEntrySetRepository,
        @Inject private readonly cacheExpiryMapper: SweepstakeCacheExpiryMapper,
        @Inject private readonly gameManager: GameManager,
        @Inject private readonly buyInBalanceCache: SweepstakeGameBuyInBalanceCache,
        @Inject private readonly eligibilityProcessor: SweepstakeEligibilityProcessor,
        @Inject private readonly buyInBalanceMapper: SweepstakeGameBuyInBalanceModelMapper,
        @Inject private readonly eventDispatcher: PlatformEventDispatcher,
        @Inject private readonly websocket: Websocket,
        @Inject private readonly nftRequirementProcessor: SweepstakeGameBuyInNFTRequirementProcessor,
        @Inject private readonly buyInBalanceManager: SweepstakeGameBuyInBalanceManager,
        @Inject private readonly userManager: UserManager) {
    }

    public async process(event: SweepstakeGameBuyInEventData): Promise<void> {
        if (event.effectiveAmountBase.isZero()) {
            Logger.info(`Game effective buy-in amount is zero. Skipping...`);
            return;
        }

        const sweepstakes = await this.getSweepstakes();

        if (sweepstakes.length === 0)
            return;

        const game = await this.gameManager.get(event.gameId);

        if (!game) {
            Logger.error(`Game ${event.gameId} not found. Skipping...`);
            return;
        }

        if (game.metadata?.sweepstakeWageringMultiplier)
            event.effectiveAmountBase = event.effectiveAmountBase.times(game.metadata.sweepstakeWageringMultiplier);

        for (const sweepstake of sweepstakes)
            await this.processEvent(sweepstake, event, game);
    }

    private async getSweepstakes(): Promise<ActiveSweepstake[]> {
        const activeSweepstakes = await this.sweepstakeManager.getAllActive({
            type: SweepstakeType.Platform,
            states: [
                SweepstakeState.Open
            ]
        });

        return _.filter(activeSweepstakes, a => !!a.metadata?.gameBuyInEntryTarget);
    }

    private async processEvent(sweepstake: ActiveSweepstake, event: SweepstakeGameBuyInEventData, game: Game): Promise<void> {
        Logger.debug(`Processing sweepstake ${sweepstake.id} game buy-in for user ${event.userId}...`, {
            gameId: game.id,
            gameProvider: event.gameProvider,
            gameBuyInBase: `$${event.amountBase}`,
            gameBuyInEffectiveBase: `$${event.effectiveAmountBase}`,
            sweepstakeId: sweepstake.id,
            sweepstakeName: sweepstake.name,
            userId: event.userId,
            rollback: event.rollback
        });

        const buyInEntryTarget = new BigNumber(sweepstake.metadata?.gameBuyInEntryTarget || 0);

        if (buyInEntryTarget.isZero()) {
            Logger.info(`Sweepstake game buy-in target is zero for sweepstake ${sweepstake.id}. Skipping...`);
            return;
        }

        const isGameEligible = this.checkGameEligibility(sweepstake, game);

        if (!isGameEligible) {
            Logger.info(`Game is not eligible for sweepstake ${sweepstake.id}. Skipping...`);
            return;
        }

        const user = await this.userManager.get(event.userId);

        if (!user)
            return;

        if (isAnonOrStandard(user) && !sweepstake.public)
            return;

        const result = await this.eligibilityProcessor.process(sweepstake, event.userId);

        if (!result.passed) {
            Logger.info(`User ${event.userId} not eligible for sweepstake ${sweepstake.id}. Skipping...`);
            return;
        }

        const nftResult = await this.nftRequirementProcessor.process(sweepstake, event.userId);

        if (!nftResult.passed) {
            Logger.info('User does not meet NFT requirements. Skipping processing...', { userId: event.userId, sweepstakeId: sweepstake.id });
            return;
        }

        const processed = await this.processBalance(sweepstake, event);

        if (!processed)
            return;

        if (processed.entryCount > 0)
            await this.addEntrySet(sweepstake, event.userId, processed.entryCount);

        if (sweepstake.metadata?.gameBuyInWebsocket)
            await this.sendWebsocket(processed.balance, sweepstake);
    }

    private async processBalance(sweepstake: ActiveSweepstake, event: SweepstakeGameBuyInEventData): Promise<SweepstakeGameBuyInBalanceResult | undefined> {
        return this.buyInBalanceCache.lock(sweepstake.id, event.userId, async () => {
            let create = false;
            let balance = await this.buyInBalanceManager.get(sweepstake.id, event.userId);

            if (balance)
                balance.amountBase = event.rollback
                    ? balance.amountBase.minus(event.effectiveAmountBase)
                    : balance.amountBase.plus(event.effectiveAmountBase);
            else {
                create = true;
                balance = {
                    sweepstakeId: sweepstake.id,
                    userId: event.userId,
                    amountBase: event.effectiveAmountBase,
                    entryCount: 0,
                    manualEntryAdditions: 0,
                    manualEntryDeductions: 0,
                    manualAmountBase: new BigNumber(0)
                };
            }

            if (create && event.rollback) {
                Logger.info('Received game buy-in rollback before balance was created. Skipping...');
                return;
            }

            // Don't need to calculate entry counts if we've received a rollback.
            if (event.rollback) {
                await this.upsertBalance(balance, sweepstake.closeTime, create);

                Logger.info(`Rolled back sweepstake ${sweepstake.id} game buy-in balance for user ${event.userId}.`, {
                    curAmountBase: `$${balance.amountBase.plus(event.effectiveAmountBase)}`,
                    newAmountBase: `$${balance.amountBase}`,
                    amountBaseChange: `-$${event.effectiveAmountBase}`,
                });

                return {
                    balance,
                    entryCount: 0
                };
            }

            const buyInEntryTarget = new BigNumber(sweepstake.metadata?.gameBuyInEntryTarget || 0);

            if (buyInEntryTarget.isZero()) {
                Logger.info('Sweepstake game buy-in target is zero. Skipping...');
                return;
            }

            const maxEntryCount = Number(sweepstake.metadata?.gameBuyInMaxEntryCount || 0);
            const buyInEntryMultiplier = Number(sweepstake.metadata?.gameBuyInEntryMultiplier || 1);
            const curEntryCount = balance.entryCount;

            let newEntryCount = balance.amountBase
                .dividedBy(buyInEntryTarget)
                .multipliedBy(buyInEntryMultiplier)
                .decimalPlaces(0, BigNumber.ROUND_DOWN)
                .toNumber();

            if (maxEntryCount > 0)
                newEntryCount = Math.min(newEntryCount, maxEntryCount);

            balance.entryCount = newEntryCount;
            await this.upsertBalance(balance, sweepstake.closeTime, create);

            const entryCount = newEntryCount - curEntryCount;

            Logger.debug(`Updated sweepstake ${sweepstake.id} game buy-in balance for user ${event.userId}.`, {
                curAmountBase: `$${balance.amountBase.minus(event.effectiveAmountBase)}`,
                newAmountBase: `$${balance.amountBase}`,
                amountBaseChange: `$${event.effectiveAmountBase}`,
                curEntryCount,
                newEntryCount,
                entryCountChange: Math.max(entryCount, 0)
            });

            return {
                balance,
                entryCount
            };
        });
    }

    private async upsertBalance(balance: SweepstakeGameBuyInBalance, expiryTime?: Date, create: boolean = false): Promise<void> {
        await this.buyInBalanceManager.upsert(balance, expiryTime, create);
    }

    private async addEntrySet(sweepstake: ActiveSweepstake, userId: number, count: number): Promise<void> {
        try {
            let sourceCurrency = sweepstake.currencyCode;

            if (sourceCurrency === Config.baseCurrency)
                sourceCurrency = Config.defaultCurrency;

            const added = await this.entrySetRepository.add({
                state: SweepstakeEntryState.Confirmed,
                sweepstakeId: sweepstake.id,
                userId,
                count,
                sourceCurrency
            });

            if (sweepstake.state <= SweepstakeState.Drawing) {
                const expireAt = await this.cacheExpiryMapper.from(sweepstake);
                await this.entrySetCache.upsert(added, expireAt);
            }

            await this.eventDispatcher.send(
                new SweepstakeEntrySetAddedEvent({
                    sweepstakeId: sweepstake.id,
                    sweepstakeType: sweepstake.type,
                    sweepstakePresentationType: sweepstake.presentationType,
                    sweepstakeMechanism: sweepstake.mechanism,
                    sweepstakeCurrencyCode: sweepstake.currencyCode,
                    userId,
                    currencyCode: sourceCurrency,
                    entrySetId: added.id,
                    entryCount: added.count,
                    cost: added.cost,
                    createTime: added.createTime,
                    silent: {
                        CRM: true
                    }
                }));

            await this.participantManager.addOrUpdate(sweepstake, userId);
            await this.sweepstakeManager.updateTotals(sweepstake);
        } catch (err) {
            Logger.error(`Failed to add game buy-in sweepstake entry set for user ${userId}`, err);
        }
    }

    private async sendWebsocket(balance: SweepstakeGameBuyInBalance, sweepstake: ActiveSweepstake): Promise<void> {
        const model = await this.buyInBalanceMapper.fromBalance(balance, sweepstake);

        if (!model) {
            Logger.warn(`Unable to map sweepstake game buy-in balance for user ${balance.userId}...`, balance);
            return;
        }

        await this.websocket
            .user(model.userId)
            .type('Sweepstake:GameBuyIn:Balance:Change')
            .payload(model)
            .send();
    }

    private checkGameEligibility(sweepstake: ActiveSweepstake, game: Game): boolean {
        const excludedGameIds = sweepstake.metadata?.excludedGameIds || [];

        if (excludedGameIds.length > 0 && excludedGameIds.includes(game.id)) {
            Logger.info(`Game ${game.id} is excluded from sweepstake ${sweepstake.id}. Skipping...`);
            return false;
        }

        const includedGameIds = sweepstake.metadata?.includedGameIds || [];

        if (includedGameIds.length > 0 && !includedGameIds.includes(game.id)) {
            Logger.info(`Game ${game.id} is not included in sweepstake ${sweepstake.id}. Skipping...`);
            return false;
        }

        const excludedGameTypes = sweepstake.metadata?.excludedGameTypes || [];

        if (excludedGameTypes.length > 0 && excludedGameTypes.includes(game.type)) {
            Logger.info(`Game ${game.id} type ${game.type} is excluded from sweepstake ${sweepstake.id}. Skipping...`);
            return false;
        }

        const includedGameTypes = sweepstake.metadata?.includedGameTypes || [];

        if (includedGameTypes.length > 0 && !includedGameTypes.includes(game.type)) {
            Logger.info(`Game ${game.id} type ${game.type} is not included in sweepstake ${sweepstake.id}. Skipping...`);
            return false;
        }

        return true;
    }
}